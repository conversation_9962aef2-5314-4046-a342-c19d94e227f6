<template>
    <view>
        <CustomNavbar :title="pageTitle" :titleColor="'##333333'" />
        <view class="main" :style="{ paddingTop: navbarTotalHeight + 'px' }">
            <view class="container">
                <view class="section">
                    <u-form :model="form" ref="uForm" :error-type="errorType" label-width="auto"
                        :label-style="labelStyle">
                        <u-form-item label="耳标号" prop="earTagNo">
                            <u-input v-model="form.earTagNo" placeholder="请输入" :custom-style="customStyle"
                                :placeholder-style="placeholderStyle" :disabled="isDetail" @blur="searchLivestock" maxlength="20" />
                        </u-form-item>
                        <u-form-item label="活畜状态" required prop="livestockSpiritState"
                            :right-icon="isDetail ? '' : 'arrow-right'">
                            <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                                :value="getSpiritStateText(form.livestockSpiritState)" placeholder="请选择" disabled
                                @click="!isDetail && (showSpiritStateSelect = true)" />
                        </u-form-item>
                        <u-form-item label="采食情况" required prop="livestockEatState"
                            :right-icon="isDetail ? '' : 'arrow-right'">
                            <u-input :custom-style="customStyle" :placeholder-style="placeholderStyle"
                                :value="getEatStateText(form.livestockEatState)" placeholder="请选择" disabled
                                @click="!isDetail && (showEatStateSelect = true)" />
                        </u-form-item>
                        <u-form-item label="异常情况" :required="isErrorDescRequired" prop="livestockErrorDesc">
                            <u-input v-model="form.livestockErrorDesc" placeholder="请输入"
                                :custom-style="customStyle" :placeholder-style="placeholderStyle"
                                :disabled="isDetail" maxlength="100" />
                        </u-form-item>
                        <u-form-item label="备注" prop="remark" :border-bottom="false" class="manageRemark">
                            <u-input v-model="form.remark" placeholder="请输入备注信息" type="textarea"
                                :custom-style="customStyle" :placeholder-style="placeholderStyle"
                                :disabled="isDetail" maxlength="200" />
                        </u-form-item>
                    </u-form>
                </view>
            </view>
        </view>

        <view v-if="!isDetail" class="bg-box">
            <view class="add-btn" @click="submitForm">提交</view>
        </view>

        <u-select v-if="!isDetail" confirm-color='#40CA8F' v-model="showSpiritStateSelect" mode="single-column"
            :list="spiritStateList" label-name="label" @confirm="selectSpiritState" value-name="value" />

        <u-select v-if="!isDetail" confirm-color='#40CA8F' v-model="showEatStateSelect" mode="single-column"
            :list="eatStateList" label-name="label" @confirm="selectEatState" value-name="value" />
    </view>
</template>

<script>
import { dailyAdd, selectEarTagNo } from '@/api/pages/livestock/underCare'
import { getDicts } from "@/api/dict.js"
import CustomNavbar from '../components/CustomNavbar.vue'

export default {
    name: 'addDailyForm',
    components: {
        CustomNavbar
    },

    data() {
        return {
            systemInfo: uni.getSystemInfoSync(),
            livestockManageId: '',
            isDetail: false,
            isSubmitting: false,
            form: {
                earTagNo: '',
                pastureId: '',
                livestockId: '',
                livestockSpiritState: '',
                livestockEatState: '',
                livestockErrorDesc: '',
                remark: '',
            },
            // 样式配置
            errorType: ['message'],
            customStyle: { textAlign: 'right', fontSize: '26rpx' },
            labelStyle: { color: '#333', fontSize: '26rpx' },
            placeholderStyle: 'text-align:right;color:#999;font-size: 26rpx;',
            // 选择器状态
            showSpiritStateSelect: false,
            showEatStateSelect: false,
            // 数据列表
            spiritStateList: [],
            eatStateList: [],
            // 活畜信息
            livestockInfo: null,
            rules: {
                livestockSpiritState: [{
                    required: true,
                    message: '请选择活畜状态',
                    trigger: ['blur', 'change']
                }],
                livestockEatState: [{
                    required: true,
                    message: '请选择采食情况',
                    trigger: ['blur', 'change']
                }],
                livestockErrorDesc: [{
                    validator: (_, value, callback) => {
                        if (this.form.livestockSpiritState && this.form.livestockSpiritState !== '1') {
                            if (!value || value.trim() === '') {
                                callback(new Error('请输入异常情况'));
                                return;
                            }
                        }
                        callback();
                    },
                    trigger: ['blur', 'change']
                }],
            }
        }
    },

    computed: {
        navbarTotalHeight() {
            return (this.systemInfo.statusBarHeight || 0) + 44;
        },
        pageTitle() {
            return this.isDetail ? '详情' : '新增日常记录';
        },
        isFormValid() {
            const { livestockSpiritState, livestockEatState, livestockId, livestockErrorDesc } = this.form;
            const basicValid = livestockSpiritState && livestockEatState && livestockId;

            // 如果活畜状态不是"正常"，还需要检查异常情况是否填写
            if (livestockSpiritState && livestockSpiritState !== '1') {
                return basicValid && livestockErrorDesc && livestockErrorDesc.trim();
            }

            return basicValid;
        },
        isErrorDescRequired() {
            // 当活畜状态不是"正常"(值为'1')时，异常情况为必填
            return this.form.livestockSpiritState && this.form.livestockSpiritState !== '1';
        },
    },

    onLoad(options) {
        this.livestockManageId = options.livestockManageId || '';
        this.isDetail = !!options.livestockManageId;

        this.getDictSpiritState();
        this.getDictEatState();

        if (this.livestockManageId) {
            this.getDetail();
        }
    },

    onReady() {
        this.$refs.uForm?.setRules?.(this.rules)
    },

    methods: {
        // 根据耳标号查询活畜信息
        async searchLivestock() {
            if (!this.form.earTagNo.trim()) {
                this.livestockInfo = null;
                this.form.livestockId = '';
                this.form.pastureId = '';
                return;
            }

            try {
                const res = await selectEarTagNo({ earTagNo: this.form.earTagNo });
                if (res.code === 200 && res.result) {
                    this.livestockInfo = res.result;
                    this.form.livestockId = res.result.livestockId;
                    this.form.pastureId = res.result.pastureId;
                } else {
                    this.$toast('未找到该耳标号对应的活畜信息');
                    this.livestockInfo = null;
                    this.form.livestockId = '';
                    this.form.pastureId = '';
                }
            } catch (error) {
                console.error('查询活畜信息失败:', error);
                this.$toast('查询活畜信息失败');
            }
        },

        // 获取活畜状态字典
        async getDictSpiritState() {
            try {
                const res = await getDicts('pasture_livestock_spirit');
                if (res && res.data) {
                    this.spiritStateList = res.data.map(item => ({
                        label: item.dictLabel,
                        value: item.dictValue
                    }));
                }
            } catch (error) {
                console.error('加载活畜状态字典失败:', error);
            }
        },

        // 获取采食情况字典
        async getDictEatState() {
            try {
                const res = await getDicts('pasture_livestock_eat');
                if (res && res.data) {
                    this.eatStateList = res.data.map(item => ({
                        label: item.dictLabel,
                        value: item.dictValue
                    }));
                }
            } catch (error) {
                console.error('加载采食情况字典失败:', error);
            }
        },

        // 获取活畜
        getSpiritStateText(value) {
            const state = this.spiritStateList.find(item => item.value === value);
            return state ? state.label : '';
        },

        // 获取采食情况
        getEatStateText(value) {
            const state = this.eatStateList.find(item => item.value === value);
            return state ? state.label : '';
        },

        // 选择活畜状态
        selectSpiritState(value) {
            if (!value?.length) return;
            this.form.livestockSpiritState = value[0].value;
            this.showSpiritStateSelect = false;
            this.resetField('livestockSpiritState');
            // 当活畜状态改变时，重新验证异常情况字段
            this.$nextTick(() => {
                this.resetField('livestockErrorDesc');
            });
        },

        // 选择采食情况
        selectEatState(value) {
            if (!value?.length) return;
            this.form.livestockEatState = value[0].value;
            this.showEatStateSelect = false;
            this.resetField('livestockEatState');
        },

        // 获取详情
        async getDetail() {
            // TODO: 日常记录详情获
            console.log('获取日常记录详情:', this.livestockManageId);
        },

        // 提交表单
        async submitForm() {
            if (this.isSubmitting) return;

            /* if (!this.form.livestockId) {
                this.$toast('请先输入耳标号查询活畜信息');
                return;
            } */

            this.isSubmitting = true;
            try {
                const valid = await this.validateForm();
                if (!valid) return;

                const submitData = {
                    earTagNo: this.form.earTagNo,
                    pastureId: this.form.pastureId,
                    livestockId: this.form.livestockId,
                    livestockSpiritState: this.form.livestockSpiritState,
                    livestockEatState: this.form.livestockEatState,
                    livestockErrorDesc: this.form.livestockErrorDesc || '',
                    remark: this.form.remark || ''
                };

                const res = await dailyAdd(submitData);

                if (res.code === 200) {
                    uni.$emit('updateDailyList');
                    this.$toast('添加成功');
                    uni.navigateBack({ delta: 1 });
                } else {
                    throw new Error(res.message || '提交失败');
                }
            } catch (error) {
                this.handleError(error, '提交失败');
            } finally {
                this.isSubmitting = false;
            }
        },

        validateForm() {
            return new Promise(resolve => this.$refs.uForm.validate(resolve));
        },

        handleError(error, customMessage = '') {
            console.error(error);
            this.$toast(error.message || customMessage || '操作失败');
        },

        resetField(prop) {
            this.$refs.uForm?.fields?.find(field => field.prop === prop)?.resetField();
        }
    },
}
</script>

<style lang="less" scoped>
@import url('../../css/index.less');

.section {
    margin-bottom: 20rpx;
}

.section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    padding: 30rpx 0 0 0;
    margin: 0 30rpx;
}

.manageRemark {
    /deep/ .u-form-item--left {
        align-items: start !important;
    }

    /deep/ .u-form-item--right {
        padding-top: 10rpx;
    }
}
</style>
