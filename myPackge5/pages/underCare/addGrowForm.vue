<template>
    <view>
        <CustomNavbar :title="pageTitle" :titleColor="'##333333'" />
        <view class="main" :style="{ paddingTop: navbarTotalHeight + 'px' }">
            <view class="section-title">基础信息</view>
            <view class="container base-container">
                <!-- 基础信息 -->
                <view class="section">
                    <u-form :model="form" ref="uForm" :error-type="errorType" label-width="auto"
                        :label-style="labelStyle">
                        <u-form-item label="耳标号" required prop="earTagNo" :border-bottom="false">
                            <u-input v-model="form.earTagNo" placeholder="请输入" :custom-style="customStyle"
                                :placeholder-style="placeholderStyle" :disabled="isDetail" @blur="searchLivestock" maxlength="20" />
                        </u-form-item>
                    </u-form>
                </view>
            </view>
            <!-- 称重信息 -->
            <view class="section-title second-section-title">称重信息</view>
            <view class="container">
                <view class="section">
                    <u-form :model="form" ref="uForm" :error-type="errorType" label-width="auto"
                        :label-style="labelStyle">
                        <u-form-item label="称重日期" required prop="operateTime" class="operateTime">
                            <text :class="form.operateTime ? 'common' : 'tips'" @click="!isDetail && showDatePicker()"
                                style="text-align: right; font-size: 26rpx;">
                                {{ form.operateTime || "请选择" }}
                            </text>
                        </u-form-item>
                        <u-form-item label="称重重量" required prop="weightCurrent">
                            <u-input v-model="form.weightCurrent" placeholder="请输入（kg）" type="number"
                                :custom-style="customStyle" :placeholder-style="placeholderStyle" :disabled="isDetail"
                                @input="handleWeightInput" maxlength="8" />
                        </u-form-item>
                        <u-form-item label="比上次增重" prop="weightIncrease" :border-bottom="false">
                            <u-input :value="form.weightIncrease" placeholder="将自动算出增重重量（kg）"
                                :custom-style="customStyle" :placeholder-style="placeholderStyle" disabled />
                        </u-form-item>
                    </u-form>
                </view>
            </view>
        </view>

        <view v-if="!isDetail" class="bg-box">
            <view class="add-btn" @click="submitForm">提交</view>
        </view>

        <u-calendar range-bg-color="rgba(64,202,143,0.13)" active-bg-color="#40CA8F" range-color="#40CA8F"
            btn-type="success" v-model="showData" :mode="`date`" @change='changeData' :max-date='maxdata'
            :min-date="mindata"></u-calendar>
    </view>
</template>

<script>
import { selectEarTagNo, growAdd } from '@/api/pages/livestock/underCare'
import CustomNavbar from '../components/CustomNavbar.vue'
export default {
    name: 'addGrowForm',
    components: {
        CustomNavbar
    },

    data() {
        return {
            systemInfo: uni.getSystemInfoSync(),
            livestockManageId: '',
            isDetail: false,
            isSubmitting: false,
            form: {
                livestockId: '',
                operateTime: '',
                weightCurrent: '',
                weightIncrease: '',
                earTagNo: '',
            },
            errorType: ['message'],
            customStyle: { textAlign: 'right', fontSize: '26rpx' },
            labelStyle: { color: '#333', fontSize: '26rpx' },
            placeholderStyle: 'text-align:right;color:#999;font-size: 26rpx;',
            mindata: "1995-09-09",
            maxdata: '2095-09-09',
            showData: false,
            livestockInfo: null,
            rules: {
                earTagNo: [{
                    required: true,
                    message: '请输入耳标号',
                    trigger: ['blur', 'change']
                }],
                operateTime: [{
                    required: true,
                    message: '请选择称重日期',
                    trigger: ['blur', 'change']
                }],
                weightCurrent: [{
                    required: true,
                    message: '请输入当前重量',
                    trigger: ['blur', 'change']
                }],
            }
        }
    },

    computed: {
        navbarTotalHeight() {
            return (this.systemInfo.statusBarHeight || 0) + 44;
        },
        pageTitle() {
            return this.isDetail ? '详情' : '新增生长检测';
        },
        isFormValid() {
            const { earTagNo, operateTime, weightCurrent, livestockId } = this.form;
            return earTagNo && operateTime && weightCurrent && livestockId;
        },
    },

    onLoad(options) {
        this.livestockManageId = options.livestockManageId || '';
        this.isDetail = !!options.livestockManageId;

        if (this.livestockManageId) {
            this.getDetail();
        }
    },

    onReady() {
        this.$refs.uForm?.setRules?.(this.rules)
    },

    methods: {
        // 根据耳标号查询活畜信息
        async searchLivestock() {
            if (!this.form.earTagNo.trim()) {
                this.livestockInfo = null;
                this.form.livestockId = '';
                this.form.weightIncrease = '';
                return;
            }

            try {
                const res = await selectEarTagNo({ earTagNo: this.form.earTagNo });
                if (res.code === 200 && res.result) {
                    this.livestockInfo = res.result;
                    this.form.livestockId = res.result.livestockId;
                    this.calculateWeightIncrease();
                } else {
                    this.$toast('未找到该耳标号对应的活畜信息');
                    this.livestockInfo = null;
                    this.form.livestockId = '';
                    this.form.weightIncrease = '';
                }
            } catch (error) {
                console.error('查询活畜信息失败:', error);
                this.$toast('查询活畜信息失败');
            }
        },

        // 处理称重重量输入
        handleWeightInput(value) {
            this.form.weightCurrent = value;
            // 使用正则表达式验证是否为正整数（不包含0）
            const positiveIntegerRegex = /^[1-9]\d*$/;
            if (value && !positiveIntegerRegex.test(value)) {
                this.form.weightIncrease = '';
                return;
            }
            // 计算增重
            if (value && positiveIntegerRegex.test(value)) {
                this.calculateWeightIncrease();
            } else {
                this.form.weightIncrease = '';
            }
        },

        // 计算比上次增重
        calculateWeightIncrease() {
            if (this.livestockInfo && this.form.weightCurrent) {
                const currentWeight = parseFloat(this.form.weightCurrent);
                const lastWeight = parseFloat(this.livestockInfo.livestockWeight || 0);
                this.form.weightIncrease = Number((currentWeight - lastWeight).toFixed(2));
            } else {
                this.form.weightIncrease = '';
            }
        },

        // 显示日期选择器
        showDatePicker() {
            this.showData = true;
        },

        changeData(e) {
            this.form.operateTime = e.result;
            this.showData = false;
            this.resetField('operateTime');
        },

        // 获取详情
        async getDetail() {
            // TODO: 实现生长检测详情获取逻辑
            // 等待后端提供详情接口
            console.log('获取生长检测详情:', this.livestockManageId);
        },



        // 提交表单
        async submitForm() {
            if (this.isSubmitting) return;

            if (!this.form.livestockId) {
                this.$toast('请先输入耳标号查询活畜信息');
                return;
            }

            this.isSubmitting = true;
            try {
                const valid = await this.validateForm();
                if (!valid) return;

                const submitData = {
                    livestockId: this.form.livestockId,
                    operateTime: this.form.operateTime,
                    weightCurrent: this.form.weightCurrent,
                    weightIncrease: this.form.weightIncrease,
                    earTagNo: this.form.earTagNo
                };

                const res = await growAdd(submitData);

                if (res.code === 200) {
                    uni.$emit('updateGrowList');
                    this.$toast('添加成功');
                    uni.navigateBack({ delta: 1 });
                } else {
                    throw new Error(res.message || '提交失败');
                }
            } catch (error) {
                this.handleError(error, '提交失败');
            } finally {
                this.isSubmitting = false;
            }
        },

        validateForm() {
            return new Promise(resolve => this.$refs.uForm.validate(resolve));
        },

        handleError(error, customMessage = '') {
            console.error(error);
            this.$toast(error.message || customMessage || '操作失败');
        },

        resetField(prop) {
            this.$refs.uForm?.fields?.find(field => field.prop === prop)?.resetField();
        }
    },
}
</script>

<style lang="less" scoped>
@import url('../../css/index.less');

.common {
    color: #333;
}

.tips {
    color: #999;
}

.section {
    margin-bottom: 20rpx;
}
.base-container{
    padding: 0 30rpx;
}
.section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    padding: 30rpx 0 0 0;
    margin: 0 30rpx;
}

.second-section-title {
    padding-top: 0;
}

.operateTime {
    /deep/ .u-form-item--right__content__slot {
        text-align: right !important;
    }
}
</style>
