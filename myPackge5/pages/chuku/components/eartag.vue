<template>
  <view class="page-container">
    <view class="u-p-b-20 box-l">
      <view class="title flex-1">
        <text class="u-m-l-15"></text>耳标列表
      </view>
      <view class="w-100">
        <u-icon name="close-circle"></u-icon>
      </view>
    </view>
    <!-- 列表项循环 -->
    <view class="scroll" v-if="eartagItem.length > 0">
      <view
        v-for="(item, index) in eartagItem"
        :key="index"
        class="card-wrapper"
        @click="choose(item)"
      >
        <view class="ch">
          <view class="checkbox">
            <u-image
              width="40rpx"
              height="40rpx"
              :src="`/static/img/${item.checked?'choose.png':'nochoose.png'}`"
            ></u-image>
          </view>
        </view>
        <view class="card">
          <!-- 卡片头部 -->
          <view class="card-header">
            <text class="header-id">{{ item.earTagNo }}</text>
            <text class="header-status"></text>
          </view>

          <!-- 卡片内容 -->
          <view class="card-content">
            <view class="info-item box-l">
              <view class="w-100 f-26 c-999">品种：</view>
              <view class="flex-1 f-26">{{ item.varietiesName }}</view>
            </view>
            <view class="info-item box-l">
              <view class="w-100 f-26 c-999">类型：</view>
              <view class="flex-1 f-26">{{ item.categoryName }}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    <view v-else class="mt-300">
      <u-empty text="当前栏位下暂无耳标数据！" mode="list"></u-empty>
    </view>
    <!-- 确定按钮 -->
    <view class="btn-box">
      <view class="confirm-btn" @click="handleConfirm">确定</view>
    </view>
  </view>
</template>

<script>
const app = getApp()
export default {
  props: {
    // 列表数据
    eartagItem: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      obs: app.globalData.obs,
    }
  },
  methods: {
    // 切换复选框状态
    // toggleCheck(index) {
    //   this.listData[index].checked = !this.listData[index].checked
    // },
    // 确定按钮事件
    handleConfirm() {
      if (this.eartagItem.length == 0) {
        this.$emit('ChooseEarTag', null)
      }

      const checkedItems = this.eartagItem.filter((item) => item.checked)

      if (checkedItems == null)
        return uni.showToast({ title: '请选择耳标', icon: 'none' })

      this.$emit('ChooseEarTag', checkedItems)

      // 这里可添加接口请求等业务逻辑
    },
    //选中元素
    choose(item) {
      item.checked = !item.checked

      //强制更新组件数据
      this.$emit('UpdateEartagItem', this.eartagItem)
    },
  },
}
</script>


<style scoped lang="less">
@import url('../../../css/index.less');
.checkbox {
  margin-top: 70rpx;
}
</style>