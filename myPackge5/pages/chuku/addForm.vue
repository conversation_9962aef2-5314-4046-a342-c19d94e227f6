<template>
  <view>
    <CustomNavbar :title="`新建出库`" :titleColor="`#333`" />
    <view class="main" :style="{ paddingTop: navbarTotalHeight + 'px' }">
      <view class="container">
        <u-form
          :model="form"
          ref="uForm"
          :error-type="errorType"
          label-width="auto"
          :label-style="labelStyle"
        >
          <u-form-item
            label="养殖场名称："
            required
            prop="pastureId"
            :right-icon="isDetail ? '' : 'arrow-right'"
          >
            <u-input
              v-model="form.pastureName"
              placeholder="请选择养殖场名称"
              :custom-style="customStyle"
              @click="(pastureSelect = true,pastureSelectType='')"
              :placeholder-style="placeholderStyle"
              disabled
            />
          </u-form-item>
          <u-form-item label="圈舍：" prop="penId" :right-icon="isDetail ? '' : 'arrow-right'">
            <u-input
              v-model="form.penName"
              placeholder="请选择圈舍"
              :custom-style="customStyle"
              @click="penSelectObj"
              :placeholder-style="placeholderStyle"
              disabled
            />
          </u-form-item>
          <u-form-item label="栏位名称：" prop="fenceCode" :right-icon="isDetail ? '' : 'arrow-right'">
            <u-input
              v-model="form.fenceCode"
              placeholder="请选择栏位名称"
              :custom-style="customStyle"
              @click="fenceSelectObj"
              :placeholder-style="placeholderStyle"
              disabled
            />
          </u-form-item>
          <view class="cultivate-section">
            <view class="section-title">出库类型：</view>
            <view class="cultivate-options">
              <view
                v-for="item in typeSortIdList"
                :key="item.value"
                :class="['cultivate-item', { 'active': form.typeSortId === item.value }]"
                @click="!isDetail && selectTypeSortIdType(item.value)"
              >{{ item.label }}</view>
            </view>
          </view>
          <u-form-item
            v-if="form.typeSortId==6"
            label="养殖场名称："
            required
            prop="pastureId"
            :right-icon="isDetail ? '' : 'arrow-right'"
          >
            <u-input
              v-model="form.newPastureName"
              placeholder="请选择养殖场名称"
              :custom-style="customStyle"
              @click="(pastureSelect = true,pastureSelectType='new')"
              :placeholder-style="placeholderStyle"
              disabled
            />
          </u-form-item>
          <u-form-item
            v-if="form.typeSortId==6"
            label="圈舍："
            prop="newPenId"
            :right-icon="isDetail ? '' : 'arrow-right'"
          >
            <u-input
              v-model="form.newPenName"
              placeholder="请选择圈舍"
              :custom-style="customStyle"
              @click="penSelectObj('new')"
              :placeholder-style="placeholderStyle"
              disabled
            />
          </u-form-item>
          <u-form-item
            v-if="form.typeSortId==6"
            label="栏位名称："
            prop="newFenceCode"
            :right-icon="isDetail ? '' : 'arrow-right'"
          >
            <u-input
              v-model="form.newFenceCode"
              placeholder="请选择栏位名称"
              :custom-style="customStyle"
              @click="fenceSelectObj('new')"
              :placeholder-style="placeholderStyle"
              disabled
            />
          </u-form-item>

          <view class="container" style="padding: 0px; margin: 25rpx 0;">
            <view class="cultivate-section">
              <view class="section-title">出库模式：</view>
              <view class="cultivate-options">
                <view
                  v-for="item in rukuMode"
                  :key="item.value"
                  :class="['cultivate-item', { 'active': form.earTagType === item.value }]"
                  @click="form.earTagType = item.value"
                >{{ item.label }}</view>
              </view>
            </view>
          </view>

          <!-- <u-form-item label="活畜数量（头）：" required prop="num" v-if="form.earTagType === '1'">
            <u-input
              v-model="form.recordList[0].num"
              maxlength="10"
              placeholder="请输入（不能超过订单数量）"
              :custom-style="customStyle"
              :placeholder-style="placeholderStyle"
            />
          </u-form-item>-->
          <view class="cultivate-section" v-if="form.earTagType === '0'">
            <view>
              <u-form-item label="活畜类别：" required prop="num" v-if="form.earTagType === '0'">
                <view style="width:100%;display:flex; align-items:center;" @click="typeClick()">
                  <view class="ear-tag-code" style="font-size:14px">请选择活畜类别</view>
                  <u-icon style="line-height:36px; " size="32" color="#c0c4cc" name="arrow-right"></u-icon>
                </view>
                <!-- <u-input
                  placeholder="请选择活畜类别"
                  :custom-style="customStyle"
                  @click="typeShow = true"
                  :placeholder-style="placeholderStyle"
                  :disabled="false"
                />-->
              </u-form-item>
              <view class="table-container" v-if="form.recordList.length > 0">
                <!-- 表格整体可横向滚动 -->
                <scroll-view class="table-scroll" scroll-x="true">
                  <view class="table-inner">
                    <!-- 表格头部 -->
                    <view class="table-header">
                      <view class="header-row">
                        <view
                          class="header-cell"
                          v-for="(column, index) in [{title:'序号',width:'15%'},
                           {title:'品种',width:'20%'},
                            {title:'类别',width:'30%'},
                           {title:'数量',width:'20%'},
                           {title:'操作',width:'15%'},]"
                          :key="index"
                          :style="{ width: column.width }"
                        >{{ column.title }}</view>
                      </view>
                    </view>

                    <!-- 表格内容 -->
                    <scroll-view
                      class="table-body"
                      scroll-y="true"
                      style="max-height:150px"
                      :scroll-top="scrollTop"
                      @scroll="onScroll"
                    >
                      <view class="table-content">
                        <view
                          class="table-row"
                          style="height: 40px;"
                          v-for="(item, rowIndex) in form.recordList"
                          :key="rowIndex"
                        >
                          <view class="table-cell" style="width:15%">{{rowIndex+1}}</view>
                          <view class="table-cell" style="width:20%">{{item.varietiesName}}</view>
                          <view class="table-cell" style="width:30%">{{item.categoryName}}</view>
                          <view class="table-cell table-input" style="width:20%;">
                            <u-input
                              maxlength="3"
                              v-model="item.num"
                              placeholder="请输入"
                              type="text"
                              style="height: 25px;z-index:2"
                            ></u-input>
                          </view>
                          <view class="table-cell" style="width:15%">
                            <view style="color:#E63131" @click="deleteAnimalsTag(rowIndex)">删除</view>
                          </view>
                        </view>
                      </view>
                    </scroll-view>
                  </view>
                </scroll-view>
              </view>
            </view>
          </view>
          <view class="cultivate-section" v-if="form.earTagType === '1'">
            <view>
              <u-form-item label="耳标编码：" required prop="num" v-if="form.earTagType === '1'">
                <view style="width:100%;display:flex; align-items:center;" @click="getEarList">
                  <view class="ear-tag-code" style="font-size:14px">请选择耳标编码</view>
                  <u-icon style="line-height:36px; " size="32" color="#c0c4cc" name="arrow-right"></u-icon>
                </view>
                <!-- <view style="width:100%;display:flex; align-items:center;">
                  <view class="ear-tag-code">请扫描</view>
                  <u-icon
                    style="line-height:36px; "
                    size="40"
                    color="#999"
                    name="scan"
                    @click="scanCode"
                  ></u-icon>
                </view>-->
              </u-form-item>
              <view class="table-container" v-if="form.earList.length>0">
                <!-- 表格整体可横向滚动 -->
                <scroll-view class="table-scroll" scroll-x="true">
                  <view class="table-inner">
                    <!-- 表格头部 -->
                    <view class="table-header">
                      <view class="header-row">
                        <view
                          class="header-cell"
                          v-for="(column, index) in [{title:'序号',width:'15%'},{title:'品种',width:'35%'}, {title:'耳标',width:'35%'},{title:'操作',width:'15%'},]"
                          :key="index"
                          :style="{ width: column.width }"
                        >{{ column.title }}</view>
                      </view>
                    </view>

                    <!-- 表格内容 -->
                    <scroll-view
                      class="table-body"
                      scroll-y="true"
                      style="max-height:150px"
                      :scroll-top="scrollTop"
                      @scroll="onScroll"
                    >
                      <view class="table-content">
                        <view
                          class="table-row"
                          v-for="(item, rowIndex) in form.earList"
                          :key="rowIndex"
                        >
                          <view class="table-cell" style="width:15%">{{rowIndex+1}}</view>
                          <view class="table-cell" style="width:35%">{{item.label}}</view>
                          <view class="table-cell" style="width:35%">{{item.value}}</view>
                          <view class="table-cell" style="width:15%">
                            <view style="color:#E63131" @click="deleteEarTag(index)">删除</view>
                          </view>
                        </view>
                      </view>
                    </scroll-view>
                  </view>
                </scroll-view>
              </view>
            </view>
          </view>
          <u-form-item label="出库日期：" required prop="operateTime" :right-icon="'arrow-right'">
            <u-input
              v-model="form.operateTime"
              placeholder="请选择出库日期"
              :custom-style="customStyle"
              :placeholder-style="placeholderStyle"
              :disabled="true"
              @click="showData = true"
            />
          </u-form-item>

          <view class="cultivate-section">
            <view class="section-title">提供检验合格证：</view>
            <view class="cultivate-options">
              <view
                v-for="item in quarantineTypeList"
                :key="item.value"
                :class="['cultivate-item', { 'active': form.quarantineType === item.value }]"
                @click="!isDetail && selectQuarantineType(item.value)"
              >{{ item.label }}</view>
            </view>
          </view>
          <u-form-item
            v-if="form.quarantineType==1"
            label="检疫合格证："
            required="true"
            prop="quarantineUrlCopy"
            :border-bottom="false"
            style="overflow: visible"
          >
            <view class="uploadImage">
              <view
                class="itemAlready"
                v-show="form.quarantineUrlCopy.length"
                v-for="(item, index) in form.quarantineUrlCopy"
                :key="index"
              >
                <image mode="scaleToFill" :src="item" @click="previewImage(item)" />
                <view class="closeIcon" @click="deleteFile('quarantineUrlCopy',index)"></view>
              </view>
              <view class="item" v-if="form.quarantineUrlCopy.length < 6">
                <u-icon
                  name="plus"
                  style="margin-left:35%;color:#eee;margin-top:15px;font-size:18px"
                ></u-icon>
                <view class="uploadIcon" @click="unloadImage('quarantineUrlCopy')">点击上传</view>
              </view>
            </view>
          </u-form-item>
          <u-form-item
            label="出发地："
            v-if="form.typeSortId==1"
            required
            prop="addressFrom"
            :right-icon="'arrow-right'"
          >
            <u-input
              v-model="form.addressFrom"
              placeholder="请选择"
              :custom-style="customStyle"
              :placeholder-style="placeholderStyle"
              @click="selectAddress(0)"
              :disabled="true"
            />
          </u-form-item>
          <u-form-item
            label="目的地："
            v-if="form.typeSortId==1"
            required
            prop="addressTo"
            :right-icon="'arrow-right'"
          >
            <u-input
              v-model="form.addressTo"
              placeholder="请选择"
              :custom-style="customStyle"
              :placeholder-style="placeholderStyle"
              @click="selectAddress(1)"
              :disabled="true"
            />
          </u-form-item>
        </u-form>
      </view>
    </view>

    <view v-if="!isDetail" class="bg-box">
      <view class="add-btn" @click="submitForm">{{ isEdit ? '保存' : '确认' }}</view>
    </view>
    <!-- 选择地址 -->
    <address-picker
      @submitAddress="submitAddress"
      :pickerAreaShow="pickerAreaShow"
      @addressCanel="pickerAreaShow = false"
      :titleShow="false"
      :showLevel3="true"
    />
    <!-- 养殖场（养殖场） -->
    <u-select
      v-if="!isDetail"
      confirm-color="#40CA8F"
      v-model="pastureSelect"
      mode="single-column"
      :list="pastureList"
      label-name="pastureName"
      @confirm="selectPasture"
      value-name="pastureId"
    />
    <!-- 圈舍 -->
    <u-select
      v-if="!isDetail"
      confirm-color="#40CA8F"
      v-model="penSelect"
      mode="single-column"
      :list="pastureSelectType=='new'?newPenList:penList"
      label-name="penName"
      @confirm="selectPen"
      value-name="penId"
    />
    <!-- 栏位 -->
    <u-select
      v-if="!isDetail"
      confirm-color="#40CA8F"
      v-model="fenceSelect"
      mode="single-column"
      :list="penSelectType=='new'?newFenceList:fenceList"
      label-name="fenceCode"
      @confirm="selectFence"
      value-name="fenceCode"
    />
    <!-- 耳标选择 -->
    <!-- <u-select
      v-if="!isDetail"
      confirm-color="#40CA8F"
      v-model="earSelect"
      mode="single-column"
      :list="eardataList"
      label-name="earcodeName"
      @confirm="selectEar"
      value-name="earTagNo"
    />-->
    <!-- 活畜类别 -->

    <!-- <u-select
      v-if="!isDetail"
      v-model="typeShow"
      confirm-color="#40CA8F"
      mode="mutil-column"
      :list="typeList"
      @confirm="confirmType"
    ></u-select>-->

    <u-select
      v-if="!isDetail"
      confirm-color="#40CA8F"
      v-model="typeNumShow"
      mode="single-column"
      :list="typeNumList"
      label-name="name"
      @confirm="confirmNumType"
      value-name="sort"
    />

    <u-calendar
      range-bg-color="rgba(64,202,143,0.13)"
      active-bg-color="#40CA8F"
      range-color="#40CA8F"
      btn-type="success"
      v-model="showData"
      :mode="`date`"
      @change="changeData"
      :max-date="maxdata"
      :min-date="mindata"
    ></u-calendar>
    <!-- 耳标 -->
    <u-popup v-model="earSelect" mode="bottom" height="85%" :closeable="true">
      <eartag
        :eartagItem="eardataList"
        @UpdateEartagItem="UpdateEartagItem"
        @ChooseEarTag="selectEar"
      ></eartag>
    </u-popup>
  </view>
</template>

<script>
import eartag from './components/eartag.vue'
import { formatTime } from '@/utils/common.js'
import addressPicker from '@/components/address-picker/index.vue'
import {
  pastureAdd,
  pastureEdit,
  pastureDetail,
} from '@/api/pages/livestock/farm'
import { chukuAdd } from '@/api/pages/livestock/chuku'
import { getDicts, livestockCategory, animalTypeList } from '@/api/dict.js'

import { uploadFiles } from '@/api/obsUpload/index'
import CustomNavbar from '../components/CustomNavbar.vue'

import {
  pasturePage,
  penPage,
  fencePage,
  earPage,
  earTagList,
} from '@/api/pages/livestock/farm'

export default {
  name: 'addFarm',
  components: {
    addressPicker,
    CustomNavbar,
    eartag,
  },

  data() {
    return {
      scrollTop: 0,
      scanFunctionIsUseable: true,
      mindata: '2024-01-01',
      maxdata: formatTime(new Date()),
      systemInfo: uni.getSystemInfoSync(),
      isIphonex: getApp().globalData.systemInfo.isIphonex,
      // 页面状态
      pastureId: '', // 养殖场ID
      pastureList: [],
      penList: [],
      fenceList: [],
      newPastureList: [],
      newPenList: [],
      newFenceList: [],
      eardataList: [],
      typeNumList: [],
      pastureSelectType: '',
      penSelectType: '',
      fenceSelectType: '',
      isEdit: false, // 是否编辑模式
      isDetail: false, // 是否详情模式
      showData: false,
      typeShow: false,
      earSelect: false,
      typeNumShow: false,
      addressType: '',
      form: {
        pastureId: null, //养殖场ID1
        pastureName: null,
        typeSortId: '1', //出库类型
        quarantineType: '0', //是否检疫
        activeType: 1,
        earList: [],
        addressTo: null, //目的地
        addressFrom: null, //出发地
        penName: null, //圈舍名称
        penId: null,
        fenceCode: null,
        quarantineUrlCopy: [], //检疫照片
        newPastureId: null, //新的养殖场id
        newPastureName: null,
        newPenId: null, //新圈舍
        newPenName: null,
        newFenceName: null,
        earTagType: '0',
        inventoryType: '1',
        operateTime: null,
        recordList: [
          // {
          //   num: '',
          //   typeId: '403292860613267456',
          //   typeName: '牛',
          //   varietiesId: '',
          //   varietiesName: '',
          //   categoryId: '',
          //   categoryName: '',
          // },
        ],
      },
      chooseEarTagList: [], //已经选择的耳标编号
      pickerAreaShow: false,
      showNatureSelect: false,
      pastureSelect: false,
      penSelect: false,
      fenceSelect: false,
      errorType: ['message'],
      customStyle: { textAlign: 'right', fontSize: '26rpx' },
      labelStyle: { color: '#333', fontSize: '26rpx' },
      placeholderStyle: 'text-align:right;color:#999;font-size: 26rpx;',
      isSubmitting: false,
      natureList: [],
      typeList: [],
      quarantineTypeList: [
        {
          value: '1',
          label: '需要提供',
        },
        {
          value: '0',
          label: '不需要提供',
        },
      ],
      typeSortIdList: [
        {
          value: '1',
          label: '销售',
        },
        {
          value: '3',
          label: '死亡',
        },
        {
          value: '5',
          label: '出栏',
        },
        {
          value: '6',
          label: '调拨',
        },
      ],
      //rukuModeVal: "1",
      rukuMode: [
        {
          value: '0',
          label: '无耳标出库',
        },
        {
          value: '1',
          label: '耳标出库',
        },
      ],

      rules: {
        'form.recordList[0].num': {
          type: 'number',
          required: true,

          message: '请输入活畜数量',
          trigger: ['blur'],
        },
        pastureName: {
          type: 'string',
          required: true,

          message: '请选择养殖场名称',
          trigger: ['blur'],
        },
      },
      tableHeight: 0,
    }
  },

  computed: {
    navbarTotalHeight() {
      const statusBarHeight = this.systemInfo.statusBarHeight || 0
      const navbarHeight = 44
      return statusBarHeight + navbarHeight
    },

    isFormValid() {
      const {
        pastureName,
        typeSortId,
        operateTime,
        fromProvinceId,
        toProvinceId,
        quarantineType,
        quarantineUrlCopy,
        earTagType,
        recordList,
        earList,
      } = this.form
      let istypeSortId = true
      if (typeSortId == 1 && !fromProvinceId && !toProvinceId) {
        istypeSortId = false
      }
      const isQuarantineType =
        (quarantineType == 0 && quarantineUrlCopy.length == 0) ||
        (quarantineType > 0 && quarantineUrlCopy.length > 0)
      const isEarTagType1 =
        (earTagType == 0 && recordList.length > 0) ||
        (earTagType == 1 && earList.length > 0)
      return (
        pastureName &&
        typeSortId &&
        operateTime &&
        istypeSortId &&
        isQuarantineType &&
        isEarTagType1
      )
    },
    // liveType() {
    //   if (
    //     !(
    //       this.form.recordList[0].varietiesName ||
    //       this.form.recordList[0].varietiesName
    //     )
    //   )
    //     return ''
    //   return `${this.form.recordList[0].varietiesName} - ${this.form.recordList[0].categoryName}`
    // },
  },

  onLoad(options) {
    // 判断页面模式
    if (options.pastureId) {
      this.pastureId = options.pastureId
      this.isEdit = options.mode === 'edit'
      this.isDetail = options.mode === 'detail'
      this.calculateTableHeight()
    }
    //初始化数据
    this.initData()
    // this.loadNatureDict();
    // this.loadCultivateDict();

    // 编辑或详情
    /* if (this.pastureId) {
            this.loadFarmDetail();
        } */
  },

  onReady() {
    this.$refs.uForm?.setRules?.(this.rules)
  },

  methods: {
    UpdateEartagItem(data) {
      console.log('UpdateEartagItem', data)
      this.eardataList = data
    },
    penSelectObj(type) {
      if (type == 'new') {
        if (!this.form.newPastureId) {
          this.$toast('请先选择调拨的养殖场')
          return
        }
        this.penSelect = true
        this.penSelectType = 'new'
      } else {
        if (!this.form.pastureId) {
          this.$toast('请先选择养殖场')
          return
        }
        this.penSelect = true
        this.penSelectType = ''
      }
    },
    fenceSelectObj(type) {
      if (type == 'new') {
        if (!this.form.newPenId) {
          this.$toast('请先选择调拨的圈舍')
          return
        }
        this.fenceSelect = true
        this.fenceSelectType = 'new'
      } else {
        if (!this.form.penId) {
          this.$toast('请先选择圈舍')
          return
        }
        this.fenceSelect = true
        this.fenceSelectType = ''
      }
    },

    // 删除检疫图片
    deleteFile(type, index) {
      if (typeof this.form[type] == 'string') {
        this.form[type] = this.form[type].split(',')
      }
      console.log(this.form[type])
      this.form[type].splice(index, 1)
      this.$forceUpdate()
      this.resetField(type)
    },
    //  上传图片
    unloadImage(type) {
      const that = this
      uni.chooseImage({
        count: 6, //默认9
        sizeType: ['original', 'compressed'], //可以指定是原图还是压缩图，默认二者都有
        sourceType: ['album', 'camera'], //从相册选择
        name: 'file',
        success: function (res) {
          uploadFiles({
            filePath: res.tempFilePaths[0],
          }).then((data) => {
            that.form[type].push(data)
            console.log(that.form[type])
            that.resetField(type)
          })
        },
        fail(e) {},
      })
    },
    // 预览图片
    previewImage(url) {
      uni.previewImage({
        urls: [url],
      })
    },
    // 滚动事件处理
    onScroll(e) {
      this.scrollTop = e.detail.scrollTop
    },
    calculateTableHeight() {
      // 使用固定高度，因为卡片组件的高度相对固定
      const windowHeight = uni.getSystemInfoSync().windowHeight
      // 估计卡片组件的高度约为300rpx，转换为px
      const cardHeight = uni.upx2px(300)
      // 减去卡片高度和一些边距
      this.tableHeight = windowHeight - cardHeight - 20

      // 或者使用延迟查询，等待组件渲染完成
      setTimeout(() => {
        const query = uni.createSelectorQuery().in(this)
        query
          .select('.info-cards')
          .boundingClientRect((data) => {
            if (data) {
              this.tableHeight = windowHeight - data.height - 20
            }
          })
          .exec()
      }, 300)
    },

    selectAddress(type) {
      this.pickerAreaShow = true
      this.addressType = type
    },

    // 扫码
    scanCode() {
      uni.scanCode({
        scanType: ['barCode'],
        success: (res) => {
          if (!this.form.earList.some((item) => item == res.result)) {
            this.form.earList.push(res.result)
            this.form.earlistNum = this.form.earList.join(',')
            this.scanCodeTxt = this.form.earList.join(',')

            // this.resetField('earlist')
            // this.resetField('earlistNum')
            console.log('res', res, this.form.earList, this.form.earlistNum)
          }
        },
        fail(e) {
          console.log('e', e)
        },
      })
    },
    // 选择耳标
    selectEar(data) {
      console.log(data)
      // return
      const that = this
      if (!data?.length) return
      // const item = value[0]
      // console.log(item, this.form['earList'])
      // const earItem = this.eardataList.find((i) => i.earTagNo == item.value)
      this.form['earList'] = data.map((item) => {
        return {
          value: item.earTagNo,
          label: `${item.varietiesName}-${item.categoryName}`,
        }
      })
      console.log(this.form['earList'])
      this.earSelect = false
    },

    /**
     * 初始化数据
     */
    async initData() {
      const pasture = await pasturePage({ pageNum: 1, pageSize: 1000 })
      if (pasture.code == 200) {
        this.pastureList = pasture.result.list
        this.newPastureList = pasture.result.list
        console.log(this.pastureList)
      }
      livestockCategory({
        pageNum: 1,
        pageSize: 100000,
        categoryType: '403292860613267456',
      }).then((res) => {
        this.typeList[0] =
          res.result.map((item) => {
            return {
              value: item.varietiesId,
              label: item.varietiesName,
            }
          }) || []
      })
      animalTypeList({
        pageSize: 9999,
        pageNum: 1,
        categoryType: '403292860613267456',
      }).then((res) => {
        this.typeList[1] =
          res.result.map((item) => {
            return {
              value: item.categoryId,
              label: item.categoryName,
            }
          }) || []
      })
    },
    changeData(e) {
      this.form.operateTime = e.result
      this.showData = false
    },

    submitAddress(val) {
      if (!val?.areaName || !val?.areaValue) return
      const areaName = val.areaName.split('-')
      const areaId = val.areaValue.split(',')
      if (this.addressType == 0) {
        //出发地

        this.form = {
          ...this.form,
          addressFrom: val.areaName,
          fromProvinceName: areaName[0] || '',
          fromProvinceId: areaId[0] || '',
          fromCityName: areaName[1] || '',
          fromCityId: areaId[1] || '',
          fromCountyName: areaName[2] || '',
          fromCountyId: areaId[2] || '',
        }
      } else {
        //目的地
        this.form = {
          ...this.form,
          addressTo: val.areaName,
          toProvinceName: areaName[0] || '',
          toProvinceId: areaId[0] || '',
          toCityName: areaName[1] || '',
          toCityId: areaId[1] || '',
          toCountyName: areaName[2] || '',
          toCountyId: areaId[2] || '',
        }
      }

      this.pickerAreaShow = false
      this.resetField('provinceName')
    },

    //  检验合格选择
    selectQuarantineType(value) {
      this.form.quarantineType = value
      this.resetField('quarantineType')
    },
    /**
     * 选择养殖场
     */
    selectPasture(value) {
      console.log('value', value)
      // pastureSelectType:'',
      // penSelectType:'',
      // fenceSelectType:'',
      if (!value?.length) return
      const item = value[0]

      if (
        this.pastureSelectType == 'new' &&
        this.form.pastureId !== item.value
      ) {
        const pastureItem = this.newPastureList.find(
          (i) => i.pastureId == item.value
        )
        this.form.newPastureName = pastureItem.pastureName
        this.form.newPastureId = item.value
        this.form.newPenName = null
        this.form.newPenId = null
        this.form.newFenceCode = null
        console.log('form', this.form)
        // this.resetField('newPastureName')
        // this.resetField('newPastureId')
        this.getPenList(pastureItem.pastureId)
      } else {
        const pastureItem = this.pastureList.find(
          (i) => i.pastureId == item.value
        )
        this.form.pastureName = pastureItem.pastureName
        this.form.pastureId = item.value
        this.form.penName = null
        this.form.penId = null
        this.form.fenceCode = null
        console.log('form', item.value, this.form)
        // this.resetField('pastureId')
        // this.resetField('pastureName')

        this.getPenList(pastureItem.pastureId)
      }

      this.pastureSelect = false
    },
    deleteEarTag(index) {
      console.log('index', index)
      this.form.earList.splice(index, 1)
      // 更新相关数据
      // this.form.earlistNum = this.form.earlist.join(',')
      console.log('deleteEarTag', this.form)
      // 重置相关字段验证
      this.resetField('earList')
      // this.resetField('earlistNum')
    },
    // 删除牛
    // 删除活畜类别记录（修复方法）
    deleteAnimalsTag(index) {
      console.log('index', index)
      // 删除 recordList 中指定索引的对象
      this.form.recordList.splice(index, 1)
      console.log('deleteAnimalsTag', this.form.recordList)
      // 如果需要，可以重置相关字段验证
      // this.resetField('recordList')
    },

    // 选择圈舍
    selectPen(value) {
      if (!value?.length) return
      const item = value[0]

      if (this.penSelectType == 'new') {
        const penItem = this.newPenList.find((i) => i.penId == item.value)
        this.form.newPenName = penItem.penName
        this.form.newPenId = item.value
        this.penSelect = false
        this.newFenceCode = ''
        console.log('form', this.form)
        // this.resetField('newPenName')
        // this.resetField('newPenId')
        this.getFencePage(penItem.penId)
      } else {
        const penItem = this.penList.find((i) => i.penId == item.value)
        this.form.penName = penItem.penName
        this.form.penId = item.value
        this.fenceCode = ''
        this.penSelect = false
        console.log('form', this.form)
        // this.resetField('penName')
        // this.resetField('penId')
        this.getFencePage(penItem.penId)
        this.getTypeList()
      }
    },
    // 选择栏位
    selectFence(value) {
      if (!value?.length) return
      const item = value[0]

      if (this.fenceSelectType == 'new') {
        // const fenceItem = this.newFenceList.find((i) => i.fenceId == item.value)

        this.form.newFenceCode = item.value
        this.fenceSelect = false
        // this.resetField('newFenceCode')
      } else {
        // const fenceItem = this.fenceList.find((i) => i.fenceId == item.value)

        this.form.fenceCode = item.value
        this.fenceSelect = false
        // this.resetField('fenceCode')
      }
    },

    // 无耳标库存牛数量
    async getTypeList() {
      const that = this

      const res = await earTagList({
        pastureId: that.form.pastureId,
        penId: that.form.penId,
        fenceCode: that.form.fenceCode,
      })
      if (res.code == 200) {
        res.result.forEach((item, index) => {
          item.name = `${item.varietiesName}-${item.categoryName}(${item.livestockNum})`
          item.sort = index
        })
        this.typeNumList = res.result
      }
    },

    // 圈舍
    async getPenList(pastureId) {
      const that = this
      console.log(pastureId)
      const res = await penPage({ pastureId })
      if (res.code == 200) {
        if (this.pastureSelectType == 'new') {
          that.newPenList = res.result.list
        } else {
          that.penList = res.result.list
        }
      }
    },
    // 栏位
    async getFencePage(pid) {
      const that = this
      console.log(pid)
      const res = await fencePage({ pid: pid, pageNum: 1, pageSize: 20 })
      if (res.code == 200) {
        if (this.penSelectType == 'new') {
          that.newFenceList = res.result.list
        } else {
          that.fenceList = res.result.list
        }
      }
    },
    typeClick() {
      if (!this.form.pastureId && !this.form.penId) {
        return this.$toast('请选选择养殖场和圈舍！')
      }
      this.typeNumShow = true
    },
    // 耳标数据

    async getEarList() {
      const that = this
      if (!this.form.pastureId && !this.form.penId) {
        return this.$toast('请选选择养殖场和圈舍！')
      }
      console.log('获取耳标数据', that.form, that.form.pastureId)
      const res = await earPage({
        pastureId: that.form.pastureId,
        penId: that.form.penId,
        fenceCode: that.form.fenceCode,
      })
      if (res.code == 200) {
        res.result.list.forEach((item) => {
          item.checked = false
        })
        that.eardataList = res.result.list
      }
      that.earSelect = true
    },
    // // 活畜
    // async getAnimalsList() {
    //   const that = this
    //   console.log('获取耳标数据', that.form, that.form.pastureId)
    //   const res = await earPage({ pastureId: that.form.pastureId })
    //   if (res.code == 200) {
    //     that.eardataList = res.result.list
    //   }
    //   that.earSelect = true
    // },

    confirmType(value) {
      console.log('value', value)
      let list = {
        varietiesId: value[0].value,
        varietiesName: value[0].label,
        categoryId: value[1].value,
        categoryName: value[1].label,
        num: null,
      }
      this.form.recordList.push(list)
      console.log('this.form.recordList', this.form.recordList)
    },
    // 无耳标出库选择牛数量
    confirmNumType(value) {
      console.log('value', value)
      let typeNumList = this.typeNumList[value[0].value]

      let list = {
        varietiesId: typeNumList.varietiesId,
        varietiesName: typeNumList.varietiesName,
        categoryId: typeNumList.categoryId,
        categoryName: typeNumList.categoryName,
        livestockNum: typeNumList.livestockNum,
        typeId: typeNumList.typeId,
        typeName: typeNumList.typeName,
        num: null,
      }
      this.form.recordList.push(list)
      console.log('this.form.recordList', this.form.recordList)
    },
    /**
     * 选择入栏类型
     * @param {Object} value
     */
    // 出库类型
    selectTypeSortIdType(value) {
      this.form.typeSortId = value
      this.resetField('typeSortId')
    },

    // 提交表单
    async submitForm() {
      if (this.isSubmitting) return
      if (!this.isFormValid) {
        return this.$toast('请填写完整信息')
      }

      try {
        this.isSubmitting = true
        const valid = await this.validateForm()
        if (!valid) return
        // 准备提交数据

        const submitData = {
          ...this.form,
        }
        if (submitData.earList.length > 0) {
          submitData.earList = submitData.earList.map((item) => item.value)
        }
        if (
          submitData.quarantineType == 1 &&
          submitData.quarantineUrlCopy.length > 0
        ) {
          submitData.quarantineUrl = submitData.quarantineUrlCopy.join(',')
        } else {
          delete submitData.quarantineUrlCopy
        }
        if (submitData.earTagType == '0') {
          delete submitData.earList
        } else {
          delete submitData.recordList
        }
        if (
          submitData.recordList.length > 0 &&
          submitData.recordList.some((item) => item.livestockNum < item.num)
        ) {
          return this.$toast('数量不能大于库存数量')
        }
        // console.log(submitData)
        // return

        submitData.addressFrom && delete submitData.addressFrom
        submitData.addressTo && delete submitData.addressTo
        submitData.addressFrom && delete submitData.addressFrom
        console.log('submitData', submitData)
        const res = await chukuAdd(submitData)

        if (res.code === 200) {
          uni.$emit('updateChukuList')
          this.$toast(this.isEdit ? '保存成功' : '添加成功')
          uni.navigateBack({ delta: 1 })
        } else {
          throw new Error(res.message || '提交失败')
        }
      } catch (error) {
        this.handleError(error, '提交失败')
      } finally {
        this.isSubmitting = false
      }
    },

    validateForm() {
      return new Promise((resolve) => {
        this.$refs.uForm.validate((valid) => resolve(valid))
      })
    },

    handleError(error, customMessage = '') {
      console.error(error)
      this.$toast(error.message || customMessage || '操作失败')
    },

    resetField(value) {
      if (!value) return
      this.$refs.uForm?.fields?.forEach((field) => {
        if (field.prop === value) {
          field.resetField()
        }
      })
    },
  },
}
</script>

<style lang="less" scoped>
@import url('../../css/index.less');
.ear-tag-code {
  display: inline-block;
  width: 88%;
  text-align: right;
  margin-right: 10px;
  color: #999;
}
.saomiao {
  // padding: 10px;
  margin-left: 10px;
  width: 18px;
  height: 18px;
  background: url(https://xmb-new02.obs.cn-north-4.myhuaweicloud.com/nmb-mini/index/saoyisao-2.png)
    no-repeat 80% 80%;
  background-size: 100% 100%;
}
.cultivate-section {
  .item-content {
    border: 1px solid #eee;
    padding: 10px;
    display: flex;
    flex-wrap: wrap;
    word-break: break-all;
    color: inherit;
    position: relative;
    .codeNum {
      position: absolute;
      bottom: 2px;
      right: 2px;
      color: #999;
    }
  }
  // 表格容器样式
  .table-container {
    // margin: 20rpx;
    background-color: #fff;
    border-radius: 10rpx;
    box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
    overflow: hidden;
    border: 1px solid #e6e8eb;
  }

  // 整体滚动容器
  .table-scroll {
    width: 100%;
    overflow-x: auto;
  }

  // 表格内部容器
  .table-inner {
    display: inline-block;
    min-width: 100%;
  }

  // 表格头部样式
  .table-header {
    // background-color: #f8f8f8;
    border-bottom: 1px solid #e6e8eb;
    position: sticky;
    color: #999;
    top: 0;
    z-index: 5;

    .header-row {
      display: flex;
    }

    .header-cell {
      padding: 14rpx 15rpx;
      font-size: 26rpx;
      font-weight: bold;
      // color: #e6e8eb;
      text-align: center;
      border-right: 1px solid #eee;

      &:last-child {
        border-right: none;
      }
    }
  }

  // 表格内容样式
  .table-body {
    .table-content {
      .table-row {
        display: flex;
        border-bottom: 1px solid #eee;

        &:last-child {
          border-bottom: none;
        }

        &:nth-child(even) {
          background-color: #f9f9f9;
        }
      }

      .table-cell {
        padding: 14rpx 15rpx;
        font-size: 26rpx;
        color: #333;
        text-align: center;
        border-right: 1px solid #eee;

        &:last-child {
          border-right: none;
        }
      }
      .table-input {
        padding: 0;
        :deep(.uni-input-input) {
          text-align: center;
          line-height: 40px;
        }
        /deep/ .uni-input-input {
          text-align: center;
          line-height: 40px;
        }
      }
    }
  }
}
.uploadImage {
  display: flex;
  flex-wrap: wrap;
  // justify-content: space-between;
  // align-content: space-between;
  position: relative;

  .itemAlready {
    width: 140rpx;
    height: 140rpx;
    border-radius: 8rpx;
    position: relative;
    margin: 0 20rpx 10rpx 0rpx;

    image,
    video {
      width: 100%;
      height: 100%;
      border-radius: 8rpx;
    }

    .closeIcon {
      width: 32rpx;
      height: 32rpx;
      background-image: url('../../../static/modalImg/error.png');
      position: absolute;
      background-size: cover;
      top: -10rpx;
      right: -10rpx;
    }
    .uploadIcon {
      width: 50px;
      height: 50px;
      border: 1px solid #eee;
    }
  }
}

.item {
  width: 140rpx;
  height: 140rpx;
  border-radius: 8rpx;
  position: relative;
  border: 2rpx dashed #d8d8d8;

  .uploadIcon {
    width: 100%;
    height: 120rpx;
    display: flex;
    justify-content: center;
    align-items: flex-end;
    font-size: 24rpx;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #d8d8d8;
    background: url('https://fengtaohui.oss-cn-beijing.aliyuncs.com/uploads/image/20230105/6400955ab08a2f639e9790b815c418db63b666a9d7e40.png')
      no-repeat;
    background-size: 20rpx 20rpx;
    background-position: center 30rpx;
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    margin: auto;
    z-index: 5;
  }
}
</style>
